"""
股票池筛选模块
用于筛选优质股票，支持多种筛选条件
"""

import akshare as ak
import pandas as pd
from typing import List, Dict, Optional
from dataclasses import dataclass
from decimal import Decimal
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class StockPoolConfig:
    """股票池筛选配置"""
    # 市值筛选（亿元）
    min_market_cap: float = 50.0  # 最小市值50亿
    max_market_cap: float = 5000.0  # 最大市值5000亿
    
    # 流动性筛选
    min_avg_volume: int = 1000000  # 最小日均成交量100万股
    min_turnover_rate: float = 0.5  # 最小换手率0.5%
    
    # 价格筛选
    min_price: float = 5.0  # 最小股价5元
    max_price: float = 200.0  # 最大股价200元
    
    # 基本面筛选
    min_pe_ratio: float = 5.0  # 最小市盈率
    max_pe_ratio: float = 50.0  # 最大市盈率
    min_pb_ratio: float = 0.5  # 最小市净率
    max_pb_ratio: float = 10.0  # 最大市净率
    
    # 技术面筛选
    min_days_listed: int = 365  # 最少上市天数1年
    exclude_st: bool = True  # 排除ST股票
    exclude_new_stock: bool = True  # 排除次新股（上市不足1年）
    
    # 行业筛选
    exclude_industries: List[str] = None  # 排除的行业
    include_industries: List[str] = None  # 包含的行业
    
    def __post_init__(self):
        if self.exclude_industries is None:
            self.exclude_industries = []
        if self.include_industries is None:
            self.include_industries = []

@dataclass
class StockInfo:
    """股票基本信息"""
    symbol: str
    name: str
    market_cap: float  # 市值（亿元）
    price: float  # 当前价格
    pe_ratio: Optional[float]  # 市盈率
    pb_ratio: Optional[float]  # 市净率
    turnover_rate: float  # 换手率
    avg_volume: int  # 日均成交量
    industry: str  # 行业
    list_date: str  # 上市日期
    is_st: bool = False  # 是否ST股票

class StockPoolSelector:
    """股票池筛选器"""
    
    def __init__(self, config: StockPoolConfig = None):
        self.config = config or StockPoolConfig()
        
    def get_all_stocks_basic_info(self) -> pd.DataFrame:
        """获取所有A股基本信息"""
        try:
            logger.info("正在获取A股基本信息...")
            # 获取A股基本信息
            stock_info = ak.stock_info_a_code_name()
            logger.info(f"获取到 {len(stock_info)} 只股票基本信息")
            return stock_info
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            raise
    
    def get_stocks_realtime_data(self, stock_codes: List[str]) -> pd.DataFrame:
        """获取股票实时行情数据"""
        try:
            logger.info(f"正在获取 {len(stock_codes)} 只股票的实时行情...")
            # 获取实时行情
            realtime_data = ak.stock_zh_a_spot_em()
            # 筛选指定股票
            filtered_data = realtime_data[realtime_data['代码'].isin(stock_codes)]
            logger.info(f"获取到 {len(filtered_data)} 只股票的实时行情")
            return filtered_data
        except Exception as e:
            logger.error(f"获取实时行情失败: {e}")
            raise
    
    def get_stocks_financial_data(self, stock_codes: List[str]) -> pd.DataFrame:
        """获取股票财务数据"""
        try:
            logger.info(f"正在获取 {len(stock_codes)} 只股票的财务数据...")
            # 这里可以根据需要获取更详细的财务数据
            # 暂时返回空DataFrame，后续可以扩展
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取财务数据失败: {e}")
            return pd.DataFrame()
    
    def apply_basic_filters(self, stock_info: pd.DataFrame) -> pd.DataFrame:
        """应用基础筛选条件"""
        logger.info("应用基础筛选条件...")
        
        # 排除ST股票
        if self.config.exclude_st:
            stock_info = stock_info[~stock_info['name'].str.contains('ST|st', na=False)]
            logger.info(f"排除ST股票后剩余: {len(stock_info)} 只")
        
        # 排除退市股票
        stock_info = stock_info[~stock_info['name'].str.contains('退', na=False)]
        logger.info(f"排除退市股票后剩余: {len(stock_info)} 只")
        
        return stock_info
    
    def apply_market_filters(self, realtime_data: pd.DataFrame) -> pd.DataFrame:
        """应用市场数据筛选条件"""
        logger.info("应用市场数据筛选条件...")
        
        # 重命名列以便处理
        data = realtime_data.copy()
        data = data.rename(columns={
            '代码': 'symbol',
            '名称': 'name', 
            '最新价': 'price',
            '市盈率-动态': 'pe_ratio',
            '市净率': 'pb_ratio',
            '换手率': 'turnover_rate',
            '成交量': 'volume',
            '总市值': 'market_cap'
        })
        
        # 转换数据类型
        numeric_columns = ['price', 'pe_ratio', 'pb_ratio', 'turnover_rate', 'volume', 'market_cap']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        # 应用筛选条件
        initial_count = len(data)
        
        # 价格筛选
        data = data[
            (data['price'] >= self.config.min_price) & 
            (data['price'] <= self.config.max_price)
        ]
        logger.info(f"价格筛选({self.config.min_price}-{self.config.max_price}元)后剩余: {len(data)} 只")
        
        # 市值筛选（转换为亿元）
        data['market_cap_billion'] = data['market_cap'] / 100000000
        data = data[
            (data['market_cap_billion'] >= self.config.min_market_cap) & 
            (data['market_cap_billion'] <= self.config.max_market_cap)
        ]
        logger.info(f"市值筛选({self.config.min_market_cap}-{self.config.max_market_cap}亿)后剩余: {len(data)} 只")
        
        # 换手率筛选
        data = data[data['turnover_rate'] >= self.config.min_turnover_rate]
        logger.info(f"换手率筛选(>={self.config.min_turnover_rate}%)后剩余: {len(data)} 只")
        
        # 成交量筛选
        data = data[data['volume'] >= self.config.min_avg_volume]
        logger.info(f"成交量筛选(>={self.config.min_avg_volume})后剩余: {len(data)} 只")
        
        # PE/PB筛选（排除异常值）
        data = data[
            (data['pe_ratio'].notna()) & 
            (data['pe_ratio'] >= self.config.min_pe_ratio) & 
            (data['pe_ratio'] <= self.config.max_pe_ratio)
        ]
        logger.info(f"PE筛选({self.config.min_pe_ratio}-{self.config.max_pe_ratio})后剩余: {len(data)} 只")
        
        data = data[
            (data['pb_ratio'].notna()) & 
            (data['pb_ratio'] >= self.config.min_pb_ratio) & 
            (data['pb_ratio'] <= self.config.max_pb_ratio)
        ]
        logger.info(f"PB筛选({self.config.min_pb_ratio}-{self.config.max_pb_ratio})后剩余: {len(data)} 只")
        
        return data
    
    def calculate_quality_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算股票质量评分"""
        logger.info("计算股票质量评分...")
        
        data = data.copy()
        
        # 初始化评分
        data['quality_score'] = 0.0
        
        # 市值评分（中等市值得分较高）
        data['market_cap_score'] = 0
        data.loc[(data['market_cap_billion'] >= 100) & (data['market_cap_billion'] <= 1000), 'market_cap_score'] = 3
        data.loc[(data['market_cap_billion'] >= 50) & (data['market_cap_billion'] < 100), 'market_cap_score'] = 2
        data.loc[(data['market_cap_billion'] >= 1000) & (data['market_cap_billion'] <= 2000), 'market_cap_score'] = 2
        data.loc[data['market_cap_billion'] > 2000, 'market_cap_score'] = 1
        
        # 估值评分（PE适中得分高）
        data['pe_score'] = 0
        data.loc[(data['pe_ratio'] >= 10) & (data['pe_ratio'] <= 25), 'pe_score'] = 3
        data.loc[(data['pe_ratio'] >= 5) & (data['pe_ratio'] < 10), 'pe_score'] = 2
        data.loc[(data['pe_ratio'] > 25) & (data['pe_ratio'] <= 35), 'pe_score'] = 2
        data.loc[data['pe_ratio'] > 35, 'pe_score'] = 1
        
        # PB评分
        data['pb_score'] = 0
        data.loc[(data['pb_ratio'] >= 1) & (data['pb_ratio'] <= 3), 'pb_score'] = 3
        data.loc[(data['pb_ratio'] >= 0.5) & (data['pb_ratio'] < 1), 'pb_score'] = 2
        data.loc[(data['pb_ratio'] > 3) & (data['pb_ratio'] <= 5), 'pb_score'] = 2
        data.loc[data['pb_ratio'] > 5, 'pb_score'] = 1
        
        # 流动性评分（换手率适中得分高）
        data['liquidity_score'] = 0
        data.loc[(data['turnover_rate'] >= 1) & (data['turnover_rate'] <= 5), 'liquidity_score'] = 3
        data.loc[(data['turnover_rate'] >= 0.5) & (data['turnover_rate'] < 1), 'liquidity_score'] = 2
        data.loc[(data['turnover_rate'] > 5) & (data['turnover_rate'] <= 10), 'liquidity_score'] = 2
        data.loc[data['turnover_rate'] > 10, 'liquidity_score'] = 1
        
        # 计算综合评分
        data['quality_score'] = (
            data['market_cap_score'] * 0.3 +
            data['pe_score'] * 0.25 +
            data['pb_score'] * 0.25 +
            data['liquidity_score'] * 0.2
        )
        
        return data
    
    def select_stock_pool(self, target_count: int = 100) -> List[StockInfo]:
        """
        选择股票池
        
        Args:
            target_count: 目标股票数量
            
        Returns:
            筛选后的股票信息列表
        """
        logger.info(f"开始筛选股票池，目标数量: {target_count}")
        
        # 1. 获取基本信息
        basic_info = self.get_all_stocks_basic_info()
        
        # 2. 应用基础筛选
        filtered_basic = self.apply_basic_filters(basic_info)
        
        # 3. 获取实时行情数据
        stock_codes = filtered_basic['code'].tolist()
        realtime_data = self.get_stocks_realtime_data(stock_codes)
        
        # 4. 应用市场数据筛选
        filtered_market = self.apply_market_filters(realtime_data)
        
        # 5. 计算质量评分
        scored_data = self.calculate_quality_score(filtered_market)
        
        # 6. 按评分排序并选择前N只
        top_stocks = scored_data.nlargest(target_count, 'quality_score')
        
        # 7. 转换为StockInfo对象
        stock_pool = []
        for _, row in top_stocks.iterrows():
            stock_info = StockInfo(
                symbol=row['symbol'],
                name=row['name'],
                market_cap=row['market_cap_billion'],
                price=row['price'],
                pe_ratio=row['pe_ratio'] if pd.notna(row['pe_ratio']) else None,
                pb_ratio=row['pb_ratio'] if pd.notna(row['pb_ratio']) else None,
                turnover_rate=row['turnover_rate'],
                avg_volume=int(row['volume']),
                industry=row.get('industry', '未知'),
                list_date=row.get('list_date', ''),
                is_st='ST' in row['name'] or 'st' in row['name']
            )
            stock_pool.append(stock_info)
        
        logger.info(f"股票池筛选完成，共选出 {len(stock_pool)} 只股票")
        
        # 打印筛选结果摘要
        self._print_selection_summary(stock_pool)
        
        return stock_pool
    
    def _print_selection_summary(self, stock_pool: List[StockInfo]):
        """打印筛选结果摘要"""
        if not stock_pool:
            return
            
        print("\n=== 股票池筛选结果摘要 ===")
        print(f"总数量: {len(stock_pool)}")
        
        # 市值分布
        market_caps = [s.market_cap for s in stock_pool]
        print(f"市值范围: {min(market_caps):.1f} - {max(market_caps):.1f} 亿元")
        print(f"平均市值: {sum(market_caps)/len(market_caps):.1f} 亿元")
        
        # 价格分布
        prices = [s.price for s in stock_pool]
        print(f"价格范围: {min(prices):.2f} - {max(prices):.2f} 元")
        print(f"平均价格: {sum(prices)/len(prices):.2f} 元")
        
        # PE分布
        pe_ratios = [s.pe_ratio for s in stock_pool if s.pe_ratio is not None]
        if pe_ratios:
            print(f"PE范围: {min(pe_ratios):.1f} - {max(pe_ratios):.1f}")
            print(f"平均PE: {sum(pe_ratios)/len(pe_ratios):.1f}")
        
        # 显示前10只股票
        print("\n前10只股票:")
        for i, stock in enumerate(stock_pool[:10]):
            print(f"{i+1:2d}. {stock.symbol} {stock.name} "
                  f"市值:{stock.market_cap:.0f}亿 "
                  f"价格:{stock.price:.2f} "
                  f"PE:{stock.pe_ratio:.1f if stock.pe_ratio else 'N/A'}")
    
    def save_stock_pool(self, stock_pool: List[StockInfo], filename: str = "stock_pool.json"):
        """保存股票池到文件"""
        try:
            # 转换为可序列化的格式
            pool_data = []
            for stock in stock_pool:
                stock_dict = {
                    'symbol': stock.symbol,
                    'name': stock.name,
                    'market_cap': stock.market_cap,
                    'price': stock.price,
                    'pe_ratio': stock.pe_ratio,
                    'pb_ratio': stock.pb_ratio,
                    'turnover_rate': stock.turnover_rate,
                    'avg_volume': stock.avg_volume,
                    'industry': stock.industry,
                    'list_date': stock.list_date,
                    'is_st': stock.is_st
                }
                pool_data.append(stock_dict)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(pool_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"股票池已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存股票池失败: {e}")
            raise
    
    def load_stock_pool(self, filename: str = "stock_pool.json") -> List[StockInfo]:
        """从文件加载股票池"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                pool_data = json.load(f)
            
            stock_pool = []
            for stock_dict in pool_data:
                stock_info = StockInfo(**stock_dict)
                stock_pool.append(stock_info)
            
            logger.info(f"从 {filename} 加载了 {len(stock_pool)} 只股票")
            return stock_pool
            
        except FileNotFoundError:
            logger.warning(f"股票池文件 {filename} 不存在")
            return []
        except Exception as e:
            logger.error(f"加载股票池失败: {e}")
            raise

def create_default_stock_pool(target_count: int = 100) -> List[StockInfo]:
    """创建默认配置的股票池"""
    config = StockPoolConfig()
    selector = StockPoolSelector(config)
    return selector.select_stock_pool(target_count)

if __name__ == "__main__":
    # 示例使用
    print("开始创建股票池...")
    
    # 创建自定义配置
    config = StockPoolConfig(
        min_market_cap=100.0,  # 最小市值100亿
        max_market_cap=3000.0,  # 最大市值3000亿
        min_price=10.0,  # 最小价格10元
        max_price=150.0,  # 最大价格150元
        min_pe_ratio=8.0,  # 最小PE 8倍
        max_pe_ratio=40.0,  # 最大PE 40倍
    )
    
    selector = StockPoolSelector(config)
    stock_pool = selector.select_stock_pool(100)
    
    # 保存股票池
    selector.save_stock_pool(stock_pool, "selected_stock_pool.json")
    
    print(f"\n股票池创建完成！共选出 {len(stock_pool)} 只股票")
